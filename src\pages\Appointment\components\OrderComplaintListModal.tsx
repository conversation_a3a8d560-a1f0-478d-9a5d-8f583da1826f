import { StatusConfig } from '@/constants/complaint';
import { complaints } from '@/services';
import { EyeOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Modal, Space, Tag, Tooltip, Typography, message } from 'antd';
import React, { useState } from 'react';
import ComplaintDetailDrawer from '../../Complaint/components/ComplaintDetailDrawer';
import ComplaintHandleModal from '../../Complaint/components/ComplaintHandleModal';

const { Text } = Typography;

interface OrderComplaintListModalProps {
  visible: boolean;
  onClose: () => void;
  order?: API.Order;
}

const OrderComplaintListModal: React.FC<OrderComplaintListModalProps> = ({
  visible,
  onClose,
  order,
}) => {
  const [detailVisible, setDetailVisible] = useState(false);
  const [handleVisible, setHandleVisible] = useState(false);
  const [currentComplaint, setCurrentComplaint] = useState<API.Complaint>();

  /** 查看投诉详情 */
  const handleViewDetail = (record: API.Complaint) => {
    setCurrentComplaint(record);
    setDetailVisible(true);
  };

  /** 处理投诉 */
  const handleProcess = (record: API.Complaint) => {
    setCurrentComplaint(record);
    setHandleVisible(true);
  };

  /** 处理投诉提交 */
  const handleProcessSubmit = async (values: {
    status: API.ComplaintStatus;
    result?: string;
    handlerId: number;
  }) => {
    if (!currentComplaint) return;

    const response = await complaints.handle(currentComplaint.id, values);
    if (response.errCode) {
      message.error(response.msg || '处理失败');
    } else {
      message.success('处理成功');
      setHandleVisible(false);
      // 刷新列表数据
      window.location.reload();
    }
  };

  /** 获取状态标签 */
  const getStatusTag = (status: string) => {
    const config = StatusConfig[status as keyof typeof StatusConfig];
    if (!config) return <Tag>{status}</Tag>;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  /** 获取分类标签 */
  const getCategoryTag = (category: string, subCategory: string) => {
    const categoryText = category === 'complaint' ? '投诉' : '建议';
    const subCategoryMap: Record<string, string> = {
      order: '订单',
      employee: '人员',
      platform: '平台',
      service: '服务',
      workflow: '流程',
    };
    const subCategoryText = subCategoryMap[subCategory] || subCategory;
    
    return (
      <Space>
        <Tag color={category === 'complaint' ? 'red' : 'blue'}>{categoryText}</Tag>
        <Tag>{subCategoryText}</Tag>
      </Space>
    );
  };

  const columns: ProColumns<API.Complaint>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 60,
      search: false,
    },
    {
      title: '标题',
      dataIndex: 'title',
      width: 200,
      search: false,
      ellipsis: true,
    },
    {
      title: '分类',
      width: 120,
      search: false,
      render: (_, record) => getCategoryTag(record.category, record.subCategory),
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      search: false,
      render: (_, record) => getStatusTag(record.status),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 160,
      search: false,
      valueType: 'dateTime',
    },
    {
      title: '操作',
      valueType: 'option',
      width: 150,
      render: (_, record) => [
        <Tooltip key="detail" title="查看详情">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          />
        </Tooltip>,
        (record.status === 'pending' || record.status === 'processing') && (
          <Tooltip key="handle" title="处理投诉">
            <Button
              type="link"
              size="small"
              icon={<ExclamationCircleOutlined />}
              onClick={() => handleProcess(record)}
            />
          </Tooltip>
        ),
      ].filter(Boolean),
    },
  ];

  return (
    <>
      <Modal
        title={
          <Space>
            <span>订单投诉记录</span>
            {order && (
              <Text type="secondary" style={{ fontSize: 14 }}>
                订单号: {order.sn}
              </Text>
            )}
          </Space>
        }
        open={visible}
        onCancel={onClose}
        footer={null}
        width={1000}
        destroyOnClose
      >
        <ProTable<API.Complaint>
          rowKey="id"
          columns={columns}
          search={false}
          pagination={{
            pageSize: 10,
            showSizeChanger: false,
            showQuickJumper: false,
          }}
          request={async () => {
            if (!order?.id) {
              return {
                data: [],
                total: 0,
                success: false,
              };
            }

            const { errCode, msg, data } = await complaints.getByOrder(order.id);
            if (errCode) {
              message.error(msg || '获取投诉记录失败');
              return {
                data: [],
                total: 0,
                success: false,
              };
            }

            return {
              data: data?.list || [],
              total: data?.list?.length || 0,
              success: true,
            };
          }}
          toolBarRender={false}
          size="small"
        />
      </Modal>

      {/* 投诉详情抽屉 */}
      <ComplaintDetailDrawer
        visible={detailVisible}
        complaint={currentComplaint}
        onClose={() => {
          setDetailVisible(false);
          setCurrentComplaint(undefined);
        }}
      />

      {/* 投诉处理模态框 */}
      <ComplaintHandleModal
        visible={handleVisible}
        complaint={currentComplaint}
        onSubmit={handleProcessSubmit}
        onClose={() => {
          setHandleVisible(false);
          setCurrentComplaint(undefined);
        }}
      />
    </>
  );
};

export default OrderComplaintListModal;
